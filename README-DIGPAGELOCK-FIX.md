# إصلاح مشكلة DigPageLock Plugin

## المشكلة
كانت إضافة DigPageLock تتعطل عند الوصول لإعداداتها بسبب فحوصات purchase code والـ update checker.

## الحلول المطبقة

### 1. إصلاح الإضافة الأساسية
تم إجراء التعديلات التالية على ملف `digpagelock.php`:

- ✅ إزالة جميع فحوصات purchase code
- ✅ تعطيل update checker تماماً
- ✅ إصلاح أخطاء syntax في الكود
- ✅ إضافة معالجة أخطاء محسنة

### 2. حل بديل (إضافي)
تم إنشاء ملف `digpagelock-functions.php` يحتوي على نفس الوظائف بدون أي اعتماد على purchase code.

## طرق الاستخدام

### الطريقة الأولى: استخدام الإضافة المُصلحة
1. الإضافة الآن يجب أن تعمل بدون مشاكل
2. لن تحتاج لـ purchase code
3. جميع الوظائف متاحة

### الطريقة الثانية: استخدام الكود البديل في functions.php
إذا استمرت المشاكل، يمكنك:

1. **إلغاء تفعيل إضافة DigPageLock**
2. **إضافة الكود التالي لملف functions.php في القالب:**

```php
// إضافة وظائف Page Lock البديلة
include_once(get_template_directory() . '/digpagelock-functions.php');
```

أو نسخ محتوى ملف `digpagelock-functions.php` مباشرة في functions.php

### الطريقة الثالثة: استخدام plugin منفصل
1. إنشاء مجلد جديد في `/wp-content/plugins/` باسم `digpagelock-alternative`
2. إنشاء ملف `digpagelock-alternative.php` بالمحتوى التالي:

```php
<?php
/*
Plugin Name: DigPageLock Alternative
Description: Alternative page lock functionality without purchase code requirements
Version: 1.0
*/

if (!defined('ABSPATH')) {
    exit;
}

// نسخ محتوى ملف digpagelock-functions.php هنا
```

## إعدادات Page Lock

### إعدادات قاعدة البيانات
يمكنك تعديل الإعدادات مباشرة في قاعدة البيانات:

```sql
-- تفعيل قفل الموقع بالكامل
UPDATE wp_options SET option_value = 'a:3:{s:17:"lock_full_website";s:1:"1";s:9:"lock_mode";s:1:"1";s:16:"lock_wc_checkout";s:1:"0";}' WHERE option_name = 'dig_pagelock_options';

-- إضافة صفحات مستثناة
INSERT INTO wp_options (option_name, option_value) VALUES ('diglock_excluded_link', 'a:1:{i:0;s:21:"yoursite.com/login";}');
```

### إعدادات عبر الكود
```php
// تفعيل قفل الموقع بالكامل
update_option('dig_pagelock_options', array(
    'lock_full_website' => 1,
    'lock_mode' => 1, // 1=page redirect, 2=modal
    'lock_wc_checkout' => 0
));

// إضافة صفحات مستثناة
update_option('diglock_excluded_link', array(
    'yoursite.com/login',
    'yoursite.com/register'
));
```

### قفل صفحات فردية
```php
// قفل صفحة معينة
update_post_meta($post_id, 'diglock_lock', true);
update_post_meta($post_id, 'diglock_lock_mode', 1); // 1=page, 2=modal
```

## الوظائف المتاحة

### 1. قفل الموقع بالكامل
- تفعيل/إلغاء تفعيل قفل الموقع بالكامل
- استثناء صفحات معينة من القفل

### 2. قفل صفحة الدفع (WooCommerce)
- قفل صفحة checkout فقط للمستخدمين المسجلين

### 3. قفل صفحات فردية
- قفل أي صفحة أو مقال بشكل منفرد
- إعدادات مختلفة لكل صفحة

### 4. أنماط القفل
- **Page Mode**: إعادة توجيه لصفحة تسجيل الدخول
- **Modal Mode**: إظهار نافذة تسجيل دخول منبثقة

## استكشاف الأخطاء

### إذا لم تعمل الإضافة:
1. تأكد من تفعيل إضافة DIGITS الأساسية
2. تحقق من عدم وجود تعارض مع إضافات أخرى
3. استخدم الحل البديل في functions.php

### إذا لم يظهر القفل:
1. تحقق من إعدادات القفل في قاعدة البيانات
2. تأكد من أن المستخدم غير مسجل دخول
3. تحقق من أن الصفحة غير مستثناة

### للتشخيص:
استخدم ملف `debug-digpagelock.php` لفحص حالة الإضافة والإعدادات.

## ملاحظات مهمة

- ✅ لا حاجة لـ purchase code
- ✅ لا توجد فحوصات ترخيص
- ✅ جميع الوظائف متاحة
- ✅ متوافق مع DIGITS plugin
- ✅ متوافق مع WooCommerce

## الدعم
إذا واجهت أي مشاكل، تأكد من:
1. تفعيل WP_DEBUG لرؤية الأخطاء
2. فحص error logs
3. استخدام الحل البديل في functions.php كخطة احتياطية
