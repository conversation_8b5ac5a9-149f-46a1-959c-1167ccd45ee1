<?php
/*
 * Debug script for DigPageLock plugin
 * Place this file in your WordPress root directory and access it via browser
 * to diagnose issues with the DigPageLock plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-config.php');
    require_once(ABSPATH . 'wp-settings.php');
}

echo '<h1>DigPageLock Debug Information</h1>';

// Check if DIGITS plugin is active
echo '<h2>1. Plugin Status</h2>';
if (is_plugin_active('digits/digit.php') || is_plugin_active('digits/digits.php')) {
    echo '<p style="color: green;">✓ DIGITS plugin is active</p>';
} else {
    echo '<p style="color: red;">✗ DIGITS plugin is NOT active</p>';
}

if (is_plugin_active('digpagelock/digpagelock.php')) {
    echo '<p style="color: green;">✓ DigPageLock plugin is active</p>';
} else {
    echo '<p style="color: red;">✗ DigPageLock plugin is NOT active</p>';
}

// Check purchase code
echo '<h2>2. Purchase Code</h2>';
$digpc = get_site_option('dig_purchasecode');
if (empty($digpc)) {
    echo '<p style="color: red;">✗ Purchase code is missing or empty</p>';
    echo '<p>Please enter your purchase code in DIGITS settings.</p>';
} else {
    echo '<p style="color: green;">✓ Purchase code is set (length: ' . strlen($digpc) . ' characters)</p>';
}

// Check license type
$license_type = get_site_option('dig_license_type', 1);
echo '<p>License type: ' . $license_type . '</p>';

// Check plugin options
echo '<h2>3. Plugin Options</h2>';
$pagelock_options = get_option('dig_pagelock_options', array());
echo '<pre>';
print_r($pagelock_options);
echo '</pre>';

$excluded_links = get_option('diglock_excluded_link', array());
echo '<h3>Excluded Links:</h3>';
echo '<pre>';
print_r($excluded_links);
echo '</pre>';

// Check for PHP errors
echo '<h2>4. PHP Error Check</h2>';
$error_log_path = ini_get('error_log');
echo '<p>Error log path: ' . $error_log_path . '</p>';

// Check WordPress debug settings
echo '<h2>5. WordPress Debug Settings</h2>';
echo '<p>WP_DEBUG: ' . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . '</p>';
echo '<p>WP_DEBUG_LOG: ' . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . '</p>';
echo '<p>WP_DEBUG_DISPLAY: ' . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'Enabled' : 'Disabled') . '</p>';

// Check if functions exist
echo '<h2>6. Function Availability</h2>';
$functions_to_check = array(
    'digpl_pagelock_values',
    'digits_addon_digpagelock',
    'digad_show_pagelock_settings',
    'dig_show_pagelock',
    'digpage_check_lock'
);

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo '<p style="color: green;">✓ ' . $function . ' exists</p>';
    } else {
        echo '<p style="color: red;">✗ ' . $function . ' does NOT exist</p>';
    }
}

// Check hooks
echo '<h2>7. WordPress Hooks</h2>';
global $wp_filter;

$hooks_to_check = array(
    'digits_settings_page',
    'digits_save_settings_data',
    'digits_addon'
);

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo '<p style="color: green;">✓ Hook "' . $hook . '" has ' . count($wp_filter[$hook]->callbacks) . ' callback(s)</p>';
    } else {
        echo '<p style="color: red;">✗ Hook "' . $hook . '" has no callbacks</p>';
    }
}

// Test pagelock values function
echo '<h2>8. Test DigPageLock Functions</h2>';
try {
    if (function_exists('digpl_pagelock_values')) {
        $values = digpl_pagelock_values();
        echo '<p style="color: green;">✓ digpl_pagelock_values() works</p>';
        echo '<pre>';
        print_r($values);
        echo '</pre>';
    } else {
        echo '<p style="color: red;">✗ digpl_pagelock_values() function not found</p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">✗ Error calling digpl_pagelock_values(): ' . $e->getMessage() . '</p>';
}

// Check memory usage
echo '<h2>9. System Information</h2>';
echo '<p>PHP Version: ' . phpversion() . '</p>';
echo '<p>WordPress Version: ' . get_bloginfo('version') . '</p>';
echo '<p>Memory Limit: ' . ini_get('memory_limit') . '</p>';
echo '<p>Current Memory Usage: ' . round(memory_get_usage() / 1024 / 1024, 2) . ' MB</p>';
echo '<p>Peak Memory Usage: ' . round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB</p>';

// Check for fatal errors in the plugin file
echo '<h2>10. Plugin File Syntax Check</h2>';
$plugin_file = WP_PLUGIN_DIR . '/digpagelock/digpagelock.php';
if (file_exists($plugin_file)) {
    echo '<p style="color: green;">✓ Plugin file exists</p>';
    
    // Basic syntax check
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($plugin_file) . " 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo '<p style="color: green;">✓ Plugin file syntax is valid</p>';
    } else {
        echo '<p style="color: red;">✗ Plugin file has syntax errors:</p>';
        echo '<pre>' . implode("\n", $output) . '</pre>';
    }
} else {
    echo '<p style="color: red;">✗ Plugin file not found at: ' . $plugin_file . '</p>';
}

echo '<h2>Debug Complete</h2>';
echo '<p>If you found any red ✗ marks above, those are likely the cause of your plugin crashes.</p>';
?>
