<?php
/**
 * Alternative DigPageLock Functions
 * Add this code to your theme's functions.php file as a backup solution
 * This provides the same functionality without relying on the plugin's purchase code checks
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Alternative page lock functionality
 * Add this to your theme's functions.php if the plugin continues to have issues
 */

// Hook into WordPress initialization
add_action('init', 'alternative_digpage_check_fullsite_lock', 10);
add_action('template_redirect', 'alternative_digpage_check_checkout_lock', 10);
add_action('get_header', 'alternative_digpagelock_lockpage', 30);

/**
 * Get page lock options (alternative version)
 */
function alternative_digpl_pagelock_values() {
    $default_values = array(
        'lock_full_website' => 0,
        'lock_mode' => 1,
        'lock_wc_checkout' => 0
    );
    
    return get_option('dig_pagelock_options', $default_values);
}

/**
 * Check if current page should be excluded from lock
 */
function alternative_dig_is_exclude_lock_page() {
    // Always exclude login pages
    if (alternative_dig_is_wp_login_page()) {
        return true;
    }
    
    $filtered_links = get_option('diglock_excluded_link', array());
    if (!empty($filtered_links) && is_array($filtered_links)) {
        $url = parse_url("//{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}");
        if (!$url) {
            return false;
        }
        $link = $url['host'];
        if (isset($url['path'])) {
            $link .= $url['path'];
        }

        if (in_array(sanitize_text_field($link), $filtered_links)) {
            return true;
        }
    }

    return false;
}

/**
 * Check if current page is WordPress login page
 */
function alternative_dig_is_wp_login_page() {
    $ABSPATH_MY = str_replace(array('\\', '/'), DIRECTORY_SEPARATOR, ABSPATH);
    
    return ((in_array($ABSPATH_MY . 'wp-login.php', get_included_files()) || 
             in_array($ABSPATH_MY . 'wp-register.php', get_included_files())) || 
            (isset($GLOBALS['pagenow']) && $GLOBALS['pagenow'] === 'wp-login.php') || 
            $_SERVER['PHP_SELF'] == '/wp-login.php');
}

/**
 * Check full site lock
 */
function alternative_digpage_check_fullsite_lock() {
    alternative_digpage_check_lock(1);
}

/**
 * Check checkout page lock
 */
function alternative_digpage_check_checkout_lock() {
    if (function_exists('is_checkout')) {
        if (is_checkout()) {
            alternative_digpage_check_lock(2);
        }
    }
}

/**
 * Main lock checking function
 * @param int $page_type 1=full site, 2=checkout, 3=individual page
 */
function alternative_digpage_check_lock($page_type) {
    if (alternative_dig_is_exclude_lock_page()) {
        return;
    }

    if (!is_user_logged_in()) {
        $digpl_pagelock_values = alternative_digpl_pagelock_values();

        $lock_full_website = $digpl_pagelock_values['lock_full_website'];
        $lock_wc_checkout = $digpl_pagelock_values['lock_wc_checkout'];
        $lock_mode = $digpl_pagelock_values['lock_mode'];

        if (($lock_full_website == 1 && $page_type == 1) ||
            ($lock_wc_checkout == 1 && $page_type == 2)) {
            
            alternative_digpage_activate_lock($page_type, $lock_mode, 0);
        }
    }
}

/**
 * Check individual page lock
 */
function alternative_digpagelock_lockpage() {
    global $wp;

    $request = $wp->request;

    if ($request == null) {
        global $post;
        if (isset($post->ID)) {
            $post_id = $post->ID;
        } else {
            return;
        }
    } else {
        $request_url = home_url($request);
        $post_id = url_to_postid($request_url);

        if ($post_id == 0 || empty($request)) {
            $post = get_page_by_path($request);
            if ($post != null) {
                $post_id = $post->ID;
            } else {
                return;
            }
        }
    }

    if ($post_id != null && !is_user_logged_in()) {
        $lock = get_post_meta($post_id, 'diglock_lock', true);
        $lock_mode = get_post_meta($post_id, 'diglock_lock_mode', true);

        if ($lock) {
            alternative_digpage_activate_lock(3, $lock_mode, $post_id);
        }
    }
}

/**
 * Activate page lock
 * @param int $type Lock type (1=full site, 2=checkout, 3=page)
 * @param int $lock_mode Lock mode (1=page, 2=modal)
 * @param int $lock_page_id Page ID being locked
 */
function alternative_digpage_activate_lock($type, $lock_mode, $lock_page_id) {
    if (wp_doing_ajax() || alternative_digpagelock_is_rest_api_request()) {
        return;
    }

    if (!is_numeric($lock_page_id)) {
        $lock_page_id = 0;
    }

    $current_url = '//' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    $is_login = (isset($_GET['login']) && $_GET['login'] == 'true') ? true : false;
    $is_login = apply_filters('is_digits_login_reg_page', $is_login, $lock_page_id, $current_url);

    if ($is_login) {
        return;
    }

    if ($lock_mode == 2) {
        // Modal mode
        $modal_filter = 'digits_modal_class_digits_native';
        $modal_filter = apply_filters('digpagelock_modal_lock', $modal_filter, $lock_page_id);
        
        add_filter($modal_filter, 'alternative_digpage_add_lockclass');
        add_action('wp_head', 'alternative_digpage_add_inlinecss');
        add_action('wp_footer', 'alternative_digpage_add_inlinecss');
        add_action('wp_footer', 'alternative_digpage_add_inlinescripts');
        
        wp_enqueue_script('jquery');
        wp_add_inline_script('jquery', "if(! /Android|webOS|iPhone|iPad|iPod|Opera Mini/i.test(navigator.userAgent) ) jQuery(document).bind('scroll',function () {window.scrollTo(0,0);});");
    } else {
        // Page redirect mode
        $login_url = add_query_arg(array(
            'login' => 'true',
            'back' => 'home'
        ), $current_url);

        $url = apply_filters('digits_pagelock_login_url', $login_url, $current_url, $lock_page_id);
        wp_safe_redirect($url);
        die();
    }
}

/**
 * Check if current request is REST API
 */
function alternative_digpagelock_is_rest_api_request() {
    if (empty($_SERVER['REQUEST_URI'])) {
        return false;
    }

    $rest_prefix = trailingslashit(rest_get_url_prefix());
    return (false !== strpos($_SERVER['REQUEST_URI'], $rest_prefix));
}

/**
 * Add CSS for locked pages
 */
function alternative_digpage_add_inlinecss() {
    ?>
    <style>
        .digits_no_dismiss {
            display: block !important;
        }
        <?php
        $digpl_pagelock_values = alternative_digpl_pagelock_values();
        if($digpl_pagelock_values['lock_full_website'] == 1){
            ?>
        .dig_login_cancel, .dig-cont-close {
            display: none;
        }
        <?php
        }
        ?>
        html, body {
            overflow: hidden;
        }
    </style>
    <?php
}

/**
 * Add JavaScript for locked pages
 */
function alternative_digpage_add_inlinescripts() {
    ?>
    <script>
        var goBack = function () {
            var ref = document.referrer;
            if (ref === '') {
                document.location.href = "/";
                return;
            }
            var host = window.location.host;
            if (ref.indexOf(host) == -1) {
                document.location.href = "/";
                return;
            }
            window.history.back()
        }

        addCloseListner('dig-cont-close');
        addCloseListner('dig_login_cancel');

        function addCloseListner(className) {
            var closeButton = document.getElementsByClassName(className);
            if (closeButton) {
                for (var i = 0; i < closeButton.length; i++) {
                    closeButton[i].addEventListener('click', goBack);
                }
            }
        }
    </script>
    <?php
}

/**
 * Add lock class to modal
 */
function alternative_digpage_add_lockclass($class) {
    $class[] = ' digits_no_dismiss';
    return $class;
}

/**
 * Save page lock settings (for admin)
 */
function alternative_digits_update_pagelock_settings() {
    if (isset($_POST['diglock_lock_full_website'])) {
        $settings = array();
        $settings['lock_full_website'] = sanitize_text_field($_POST['diglock_lock_full_website']);
        $settings['lock_wc_checkout'] = sanitize_text_field($_POST['diglock_lock_wc_checkout']);
        $settings['lock_mode'] = sanitize_text_field($_POST['diglock_lock_method']);
        
        update_option('dig_pagelock_options', $settings);

        if (isset($_POST['diglock_excluded_link'])) {
            $excluded_links = $_POST['diglock_excluded_link'];
            $filtered_links = array();
            
            foreach ($excluded_links as $link) {
                $url = parse_url($link);
                if (!$url) {
                    continue;
                }
                $link = $url['host'];
                if (isset($url['path'])) {
                    $link .= $url['path'];
                }
                $filtered_links[] = sanitize_text_field($link);
            }
            
            update_option('diglock_excluded_link', $filtered_links);
        }
    }
}

// Hook the settings save function
add_action('digits_save_settings_data', 'alternative_digits_update_pagelock_settings');

?>
